<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="?android:colorBackground">

    <com.amazon.meridian.text.MeridianText
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="16dp"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="16dp"
            android:textSize="20sp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:meridianTextType="h300" />

    <FrameLayout
            android:id="@+id/content_container"
            android:layout_width="0dp"
            android:layout_height="0dp"
            android:layout_marginTop="16dp"
            android:layout_marginBottom="16dp"
            app:layout_constraintBottom_toTopOf="@id/button_container"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/title">

        <ImageView
                android:id="@+id/photo"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="16dp"
                android:scaleType="fitCenter" />

        <com.amazon.maps.engagement.widget.annotation.AnnotationFrameLayout
                android:id="@+id/annotation_container"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:layout_marginHorizontal="16dp" />

    </FrameLayout>

    <LinearLayout
            android:id="@+id/shapes_submenu"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:background="?android:colorBackground"
            android:elevation="4dp"
            android:orientation="horizontal"
            android:padding="8dp"
            android:visibility="invisible"
            app:layout_constraintBottom_toTopOf="@id/button_container"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintEnd_toEndOf="parent">

        <ImageButton
                android:id="@+id/pin_btn"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:background="@drawable/annotation_button_background"
                android:contentDescription="Pin"
                android:padding="8dp"
                android:src="@drawable/annotation_geo_pin_item"
                app:tint="?android:attr/textColorPrimary" />

        <ImageButton
                android:id="@+id/rect_btn"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:background="@drawable/annotation_button_background"
                android:contentDescription="Rectangle"
                android:padding="8dp"
                android:src="@drawable/annotation_square_item"
                app:tint="?android:attr/textColorPrimary" />

        <ImageButton
                android:id="@+id/circle_btn"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:background="@drawable/annotation_button_background"
                android:contentDescription="Circle"
                android:padding="8dp"
                android:src="@drawable/annotation_circle_item"
                app:tint="?android:attr/textColorPrimary" />

        <ImageButton
                android:id="@+id/cross_btn"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:background="@drawable/annotation_button_background"
                android:contentDescription="Cross"
                android:padding="8dp"
                android:src="@drawable/annotation_cross_item"
                app:tint="#FF0000" />

        <ImageButton
                android:id="@+id/check_btn"
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:layout_marginStart="8dp"
                android:background="@drawable/annotation_button_background"
                android:contentDescription="Check"
                android:padding="8dp"
                android:src="@drawable/annotation_check_item"
                app:tint="#00FF00" />

    </LinearLayout>

    <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/button_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:paddingTop="16dp"
            android:paddingBottom="16dp"
            app:layout_constraintBottom_toBottomOf="parent">

        <LinearLayout
                android:id="@+id/shapes_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintEnd_toStartOf="@id/lines_container"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintHorizontal_chainStyle="spread">

            <ImageButton
                    android:id="@+id/shapes_btn"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?android:selectableItemBackgroundBorderless"
                    android:contentDescription="Shapes"
                    android:src="@drawable/tab_bar_item_shapes"
                    app:tint="?android:attr/textColorPrimary" />

            <!--    TODO: Translate hardcoded text -->
            <com.amazon.meridian.text.MeridianText
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Shapes"
                    android:textSize="12sp"
                    app:meridianTextType="b200" />
        </LinearLayout>

        <LinearLayout
                android:id="@+id/lines_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                app:layout_constraintStart_toEndOf="@id/shapes_container"
                app:layout_constraintEnd_toStartOf="@id/text_container"
                app:layout_constraintTop_toTopOf="parent">

            <ImageButton
                    android:id="@+id/lines_btn"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?android:selectableItemBackgroundBorderless"
                    android:contentDescription="Lines"
                    android:src="@drawable/annotation_arrow_item"
                    app:tint="?android:attr/textColorPrimary" />

            <!--    TODO: Translate hardcoded text -->
            <com.amazon.meridian.text.MeridianText
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Lines"
                    android:textSize="12sp"
                    app:meridianTextType="b200" />
        </LinearLayout>

        <LinearLayout
                android:id="@+id/text_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                app:layout_constraintStart_toEndOf="@id/lines_container"
                app:layout_constraintEnd_toStartOf="@id/undo_container"
                app:layout_constraintTop_toTopOf="parent">

            <ImageButton
                    android:id="@+id/text_btn"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?android:selectableItemBackgroundBorderless"
                    android:contentDescription="Text"
                    android:src="@drawable/tab_bar_item_text"
                    app:tint="?android:attr/textColorPrimary" />

            <!--    TODO: Translate hardcoded text -->
            <com.amazon.meridian.text.MeridianText
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Text"
                    android:textSize="12sp"
                    app:meridianTextType="b200" />
        </LinearLayout>

        <LinearLayout
                android:id="@+id/undo_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                app:layout_constraintStart_toEndOf="@id/text_container"
                app:layout_constraintEnd_toStartOf="@id/save_container"
                app:layout_constraintTop_toTopOf="parent">

            <ImageButton
                    android:id="@+id/undo_btn"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?android:selectableItemBackgroundBorderless"
                    android:contentDescription="Undo"
                    android:src="@drawable/tab_bar_item_undo"
                    app:tint="?android:attr/textColorPrimary" />

            <!--    TODO: Translate hardcoded text -->
            <com.amazon.meridian.text.MeridianText
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Undo"
                    android:textSize="12sp"
                    app:meridianTextType="b200" />
        </LinearLayout>

        <LinearLayout
                android:id="@+id/save_container"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:gravity="center"
                app:layout_constraintStart_toEndOf="@id/undo_container"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent">

            <ImageButton
                    android:id="@+id/save_btn"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?android:selectableItemBackgroundBorderless"
                    android:contentDescription="Save"
                    android:src="@drawable/tab_bar_item_save"
                    app:tint="?android:attr/textColorPrimary" />

            <!--    TODO: Translate hardcoded text -->
            <com.amazon.meridian.text.MeridianText
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="Save"
                    android:textSize="12sp"
                    app:meridianTextType="b200" />
        </LinearLayout>

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.constraintlayout.widget.ConstraintLayout>
