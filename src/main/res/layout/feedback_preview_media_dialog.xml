<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:app="http://schemas.android.com/apk/res-auto"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_margin="@dimen/meridianSpacing400"
        android:background="?attr/meridianThemeBackgroundSecondaryDefault">

    <com.amazon.meridian.text.MeridianText
            android:id="@+id/title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:padding="@dimen/meridianSpacing400"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            app:meridianTextType="h300"
            tools:text="Photo of the problem" />

    <ImageView
            android:id="@+id/photo"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:adjustViewBounds="true"
            android:scaleType="fitCenter"
            app:layout_constraintBottom_toTopOf="@id/footer"
            app:layout_constraintTop_toBottomOf="@id/title" />

    <com.amazon.rds.footer.RDSFooter
            android:id="@+id/footer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            app:layout_constraintBottom_toBottomOf="parent">

        <com.amazon.rds.buttonlayout.RDSButtonLayout
                android:id="@+id/previewLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:isHorizontal="true"
                app:primaryButtonText="@string/feedback_close"
                app:secondaryButtonText="@string/feedback_delete" />
    </com.amazon.rds.footer.RDSFooter>

</androidx.constraintlayout.widget.ConstraintLayout>
