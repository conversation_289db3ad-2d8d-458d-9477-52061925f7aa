<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <!-- Pressed State -->
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#E0E0E0" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- Selected State -->
    <item android:state_selected="true">
        <shape android:shape="rectangle">
            <solid android:color="#BBDEFB" />
            <corners android:radius="8dp" />
        </shape>
    </item>
    
    <!-- Default State -->
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@android:color/transparent" />
            <corners android:radius="8dp" />
        </shape>
    </item>
</selector>
