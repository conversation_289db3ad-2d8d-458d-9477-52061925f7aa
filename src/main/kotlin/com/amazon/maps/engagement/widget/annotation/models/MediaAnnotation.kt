package com.amazon.maps.engagement.widget.annotation.models

import android.view.View
import com.amazon.maps.engagement.sdk.R

enum class AnnotationState {
    DRAWN,
    NOT_DRAWN,
    MOVED,
    SCALED,
    DELETED
}

/** This class will be used to create a View representation of Annotations.
 * Each instance contains properties that will be used to create custom Views. */
open class MediaAnnotation {
    val id = View.generateViewId()
    var state = AnnotationState.NOT_DRAWN
    open val imageResource: Int? = null
    var x: Float = X
    var y: Float = Y
    open var width: Int = WIDTH
    open var height: Int = HEIGHT
    var colorID = R.color.annotation_color
    var visibility: Int = View.VISIBLE

    open class Shapes : MediaAnnotation() {
        class Pin : Shapes() {
            override val imageResource = R.drawable.annotation_geo_pin_item
        }

        class Rectangle : Shapes() {
            override val imageResource = R.drawable.annotation_square_item
        }

        class Circle : Shapes() {
            override val imageResource = R.drawable.annotation_circle_item
        }

        class Cross : Shapes() {
            override val imageResource = R.drawable.annotation_cross_item
        }

        class Arrow : Shapes() {
            override val imageResource = R.drawable.annotation_arrow_item
        }

        class Check : Shapes() {
            override val imageResource = R.drawable.annotation_check_item
        }
    }

    class Text : MediaAnnotation() {
        override var width = TEXT_WIDTH
        override var height = TEXT_HEIGHT
    }

    companion object {
        // Default values for Annotations
        const val X = 150f
        const val Y = 150f
        const val WIDTH = 300
        const val HEIGHT = 300
        const val TEXT_WIDTH = 450
        const val TEXT_HEIGHT = 250
    }
}
